package net.summerfarm.wms.infrastructure.dao.external.dataobject;

import java.time.LocalDateTime;
import lombok.Data;


/**
 * <AUTHOR>
 * @date 2025-05-21 17:13:30
 * @version 1.0
 *
 */
@Data
public class WmsExternalOrderMapping {
	/**
	 * primary key
	 */
	private Long id;

	/**
	 * create time
	 */
	private LocalDateTime createTime;

	/**
	 * update time
	 */
	private LocalDateTime updateTime;

	/**
	 * 外部单号
	 */
	private String externalNo;

	/**
	 * 业务单号
	 */
	private String bizNo;

	/**
	 * 内部单号
	 */
	private String internalNo;

	/**
	 * 业务类型
	 */
	private Integer type;

	/**
	 * appkey
	 */
	private String appKey;

	/**
	 * 库存仓
	 */
	private Integer warehouseNo;

	/**
	 * 租户id
	 */
	private Long tenantId;

	/**
	 * 仓库租户id
	 */
	private Long warehouseTenantId;



}